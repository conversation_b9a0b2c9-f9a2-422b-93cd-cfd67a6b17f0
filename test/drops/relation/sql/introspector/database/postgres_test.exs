defmodule Drops.Relation.SQL.Introspector.Database.PostgresTest do
  use Drops.RelationCase, async: false

  alias Drops.Relation.SQL.Introspector.Database.Postgres
  alias Drops.Relation.Schema.Indices

  describe "get_table_indices/2" do
    @tag relations: [:postgres_types], adapter: :postgres
    test "extracts indices from PostgreSQL database", %{repo: repo} do
      # Create a test table with indices
      Ecto.Adapters.SQL.query!(
        repo,
        """
        DROP TABLE IF EXISTS test_postgres_indices
        """,
        []
      )

      Ecto.Adapters.SQL.query!(
        repo,
        """
        CREATE TABLE test_postgres_indices (
          id SERIAL PRIMARY KEY,
          email VARCHAR(255) UNIQUE,
          name VARCHAR(255),
          status VARCHAR(50)
        )
        """,
        []
      )

      Ecto.Adapters.SQL.query!(
        repo,
        "CREATE INDEX idx_test_postgres_name ON test_postgres_indices(name)",
        []
      )

      Ecto.Adapters.SQL.query!(
        repo,
        "CREATE INDEX idx_test_postgres_status_name ON test_postgres_indices(status, name)",
        []
      )

      # Test index extraction
      {:ok, indices} = Postgres.get_table_indices(repo, "test_postgres_indices")

      assert %Indices{} = indices
      assert length(indices.indices) >= 2

      # Find the specific indices we created
      name_index = Enum.find(indices.indices, &(&1.name == "idx_test_postgres_name"))

      composite_index =
        Enum.find(indices.indices, &(&1.name == "idx_test_postgres_status_name"))

      assert name_index
      assert length(name_index.fields) == 1
      assert hd(name_index.fields).name == :name
      assert name_index.unique == false

      assert composite_index
      assert length(composite_index.fields) == 2
      assert Enum.map(composite_index.fields, & &1.name) == [:status, :name]
      assert composite_index.unique == false

      # Clean up
      Ecto.Adapters.SQL.query!(repo, "DROP TABLE test_postgres_indices", [])
    end

    @tag relations: [:postgres_types], adapter: :postgres
    test "handles table with no custom indices", %{repo: repo} do
      # Create a simple table with no custom indices
      Ecto.Adapters.SQL.query!(
        repo,
        """
        DROP TABLE IF EXISTS test_postgres_no_indices
        """,
        []
      )

      Ecto.Adapters.SQL.query!(
        repo,
        """
        CREATE TABLE test_postgres_no_indices (
          id SERIAL PRIMARY KEY,
          data TEXT
        )
        """,
        []
      )

      {:ok, indices} = Postgres.get_table_indices(repo, "test_postgres_no_indices")

      assert %Indices{} = indices
      # Should have no custom indices (primary key indices are excluded)
      assert length(indices.indices) == 0

      # Clean up
      Ecto.Adapters.SQL.query!(repo, "DROP TABLE test_postgres_no_indices", [])
    end

    @tag relations: [:postgres_types], adapter: :postgres
    test "returns empty indices for non-existent table", %{repo: repo} do
      # PostgreSQL should return empty results for non-existent tables
      {:ok, indices} = Postgres.get_table_indices(repo, "non_existent_table")
      assert %Indices{} = indices
      assert indices.indices == []
    end
  end

  describe "introspect_table_columns/2" do
    @tag relations: [:postgres_types], adapter: :postgres
    test "extracts column information from PostgreSQL table", %{repo: repo} do
      # Create a test table
      Ecto.Adapters.SQL.query!(
        repo,
        """
        DROP TABLE IF EXISTS test_postgres_columns
        """,
        []
      )

      Ecto.Adapters.SQL.query!(
        repo,
        """
        CREATE TABLE test_postgres_columns (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          email VARCHAR(255) UNIQUE,
          age INTEGER,
          active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT NOW()
        )
        """,
        []
      )

      columns = Postgres.introspect_table_columns(repo, "test_postgres_columns")

      assert is_list(columns)
      assert length(columns) == 6

      # Check specific columns
      id_column = Enum.find(columns, &(&1.name == "id"))
      name_column = Enum.find(columns, &(&1.name == "name"))
      email_column = Enum.find(columns, &(&1.name == "email"))

      assert id_column
      assert id_column.type == "integer"
      assert id_column.primary_key == true

      assert name_column
      assert name_column.type == "character varying"
      assert name_column.not_null == true

      assert email_column
      assert email_column.type == "character varying"

      # Clean up
      Ecto.Adapters.SQL.query!(repo, "DROP TABLE test_postgres_columns", [])
    end
  end

  describe "db_type_to_ecto_type/2" do
    test "converts PostgreSQL types to Ecto types correctly" do
      # Integer types
      assert Postgres.db_type_to_ecto_type("integer", "id") == :integer
      assert Postgres.db_type_to_ecto_type("bigint", "big_id") == :integer
      assert Postgres.db_type_to_ecto_type("smallint", "small_id") == :integer
      assert Postgres.db_type_to_ecto_type("int2", "tiny_value") == :integer
      assert Postgres.db_type_to_ecto_type("int4", "value") == :integer
      assert Postgres.db_type_to_ecto_type("int8", "big_value") == :integer

      # Floating point types
      assert Postgres.db_type_to_ecto_type("real", "price") == :float
      assert Postgres.db_type_to_ecto_type("float4", "small_float") == :float
      assert Postgres.db_type_to_ecto_type("double precision", "big_float") == :float
      assert Postgres.db_type_to_ecto_type("float8", "precise_float") == :float

      # Decimal types
      assert Postgres.db_type_to_ecto_type("numeric", "amount") == :decimal
      assert Postgres.db_type_to_ecto_type("decimal", "precise_amount") == :decimal
      assert Postgres.db_type_to_ecto_type("money", "currency") == :decimal

      # String types
      assert Postgres.db_type_to_ecto_type("character varying", "name") == :string
      assert Postgres.db_type_to_ecto_type("varchar", "title") == :string
      assert Postgres.db_type_to_ecto_type("character", "code") == :string
      assert Postgres.db_type_to_ecto_type("char", "status") == :string
      assert Postgres.db_type_to_ecto_type("text", "content") == :string

      # Date/time types
      assert Postgres.db_type_to_ecto_type("date", "birth_date") == :date
      assert Postgres.db_type_to_ecto_type("time", "start_time") == :time
      assert Postgres.db_type_to_ecto_type("timestamp", "created_at") == :naive_datetime
      assert Postgres.db_type_to_ecto_type("timestamp with time zone", "updated_at") == :utc_datetime
      assert Postgres.db_type_to_ecto_type("timestamptz", "logged_at") == :utc_datetime

      # Special types
      assert Postgres.db_type_to_ecto_type("boolean", "active") == :boolean
      assert Postgres.db_type_to_ecto_type("uuid", "identifier") == :binary_id
      assert Postgres.db_type_to_ecto_type("json", "metadata") == :map
      assert Postgres.db_type_to_ecto_type("jsonb", "data") == :map
      assert Postgres.db_type_to_ecto_type("bytea", "binary_data") == :binary

      # Array types
      assert Postgres.db_type_to_ecto_type("integer[]", "numbers") == {:array, :integer}
      assert Postgres.db_type_to_ecto_type("text[]", "tags") == {:array, :string}
      assert Postgres.db_type_to_ecto_type("boolean[]", "flags") == {:array, :boolean}

      # Network types (mapped to string)
      assert Postgres.db_type_to_ecto_type("inet", "ip_address") == :string
      assert Postgres.db_type_to_ecto_type("cidr", "network") == :string
      assert Postgres.db_type_to_ecto_type("macaddr", "mac_address") == :string

      # Unknown type fallback
      assert Postgres.db_type_to_ecto_type("unknown_type", "field") == :string
    end
  end

  describe "index_type_to_atom/1" do
    test "converts PostgreSQL index types to atoms" do
      assert Postgres.index_type_to_atom("btree") == :btree
      assert Postgres.index_type_to_atom("hash") == :hash
      assert Postgres.index_type_to_atom("gin") == :gin
      assert Postgres.index_type_to_atom("gist") == :gist
      assert Postgres.index_type_to_atom("brin") == :brin
      assert Postgres.index_type_to_atom("unknown") == nil
    end
  end
end
